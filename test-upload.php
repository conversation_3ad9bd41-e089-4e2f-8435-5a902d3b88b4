<?php
// Тестовый файл для проверки прав доступа к uploads
$upload_dir = wp_upload_dir();

echo "Upload directory test:\n";
echo "Path: " . $upload_dir['path'] . "\n";
echo "URL: " . $upload_dir['url'] . "\n";
echo "Base dir: " . $upload_dir['basedir'] . "\n";

echo "\nDirectory exists: " . (file_exists($upload_dir['path']) ? 'Yes' : 'No') . "\n";
echo "Directory writable: " . (is_writable($upload_dir['path']) ? 'Yes' : 'No') . "\n";

// Пробуем создать тестовый файл
$test_file = $upload_dir['path'] . '/test.txt';
$result = file_put_contents($test_file, 'Test content');

if ($result !== false) {
    echo "Test file created successfully\n";
    unlink($test_file); // Удаляем тестовый файл
} else {
    echo "Failed to create test file\n";
    echo "Error: " . error_get_last()['message'] . "\n";
}

// Проверяем права доступа
if (function_exists('fileperms')) {
    $perms = fileperms($upload_dir['path']);
    echo "Directory permissions: " . substr(sprintf('%o', $perms), -4) . "\n";
}
?>
