<?php
class TCP_Parser {
    private $channel_name;
    private $proxy;
    private $timeout;

    public function __construct($channel_name, $proxy = '', $timeout = 30) {
        $this->channel_name = str_replace('@', '', $channel_name);
        $this->proxy = $proxy;
        $this->timeout = $timeout;
    }

    public function get_last_posts($limit = 10) {
        $url = "https://t.me/s/{$this->channel_name}";
        $html = $this->fetch_html($url);

        if (!$html) {
            return new WP_Error('tcp_fetch_error', 'Не удалось загрузить канал');
        }

        return $this->parse_html($html, $limit);
    }

    private function fetch_html($url) {
        $args = [
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'timeout' => $this->timeout,
        ];

        if (!empty($this->proxy)) {
            $args['proxy'] = $this->proxy;
        }

        $response = wp_remote_get($url, $args);
        
        if (is_wp_error($response)) {
            error_log('TCP Error: ' . $response->get_error_message());
            return false;
        }

        return wp_remote_retrieve_body($response);
    }

    private function parse_html($html, $limit) {
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));

        $xpath = new DOMXPath($dom);
        $posts = [];
        
        // Ищем все сообщения
        $message_nodes = $xpath->query("//div[contains(@class, 'tgme_widget_message') and @data-post]");

        foreach ($message_nodes as $node) {
            if (count($posts) >= $limit) break;

            // Проверяем, что это DOMElement
            if (!($node instanceof DOMElement)) continue;

            // Извлекаем ID поста
            $post_id = $node->getAttribute('data-post');
            $post_id = explode('/', $post_id)[1] ?? $post_id;

            // Извлекаем текст
            $text = '';
            $text_node = $xpath->query(".//div[contains(@class, 'tgme_widget_message_text')]", $node);
            if ($text_node->length > 0) {
                $text = trim($text_node->item(0)->textContent);
                $text = preg_replace('/\s+/', ' ', $text); // Удаляем лишние пробелы
            }

            // Извлекаем изображение (если есть)
            $image = '';
            $image_wrap = $xpath->query(".//a[contains(@class, 'tgme_widget_message_photo_wrap')]", $node);
            if ($image_wrap->length > 0) {
                $image_element = $image_wrap->item(0);
                if ($image_element instanceof DOMElement) {
                    $style = $image_element->getAttribute('style');
                    if (preg_match('/background-image:url\(\'(.*?)\'\)/', $style, $matches)) {
                        $image = $matches[1];
                    }
                }
            }

            // Формируем данные поста
            $posts[] = [
                'id' => $post_id,
                'text' => $text,
                'image' => $image
            ];
        }

        return $posts;
    }
}