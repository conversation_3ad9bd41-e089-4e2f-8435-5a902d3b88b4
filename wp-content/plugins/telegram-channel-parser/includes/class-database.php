<?php
class TCP_Database {
    public static function activate() {
        // При активации плагина
    }

    public static function deactivate() {
        // При деактивации
    }

    public static function save_post($post_data) { 

        print_r("\n");
        print_r($post_data['text'] . "\n");
        print_r($post_data['image'] . "\n");

        // Создаем новый пост
        $post_id = wp_insert_post([
            'post_title' => wp_trim_words($post_data['text'], 10, '...'),
            'post_content' => $post_data['text'],
            'post_status' => 'publish',
            'post_type' => 'post',
        ]);

        if (is_wp_error($post_id) || !$post_id) {
            error_log('TCP Error: Failed to create post');
            return false;
        }

        // Сохраняем метаданные
        update_post_meta($post_id, 'tcp_message_id', $post_data['id']);
        update_post_meta($post_id, 'tcp_channel', $post_data['channel']);

        // Загружаем изображение и устанавоиваем его как миниатюру
        if (!empty($post_data['image'])) {
            print_r('Uploading image: ' . $post_data['image'] . "\n");
            self::upload_media($post_data['image'], $post_id);
        }

        return $post_id;
    }

    private static function upload_media($image_url, $post_id) {
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');

        $media_id = media_sideload_image($image_url, $post_id, null, 'id');
        
        if (!is_wp_error($media_id)) {
            set_post_thumbnail($post_id, $media_id);
            print_r('Image uploaded and set as thumbnail' . "\n");
        } else {
            print_r('Failed to upload image: ' . $media_id->get_error_message() . "\n");
            error_log('TCP Media Error: ' . $media_id->get_error_message());
        }
    }
}